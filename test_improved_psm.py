import pandas as pd
import numpy as np
import logging
from sklearn.neighbors import NearestNeighbors
import pickle
import time
from datetime import datetime
from typing import Dict, List, Tuple

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def compile_control_group_psm_knn(
    treatment_repos_with_left_date: pd.DataFrame,
    candidate_repos: List[str],
    productivity_metric_data: pd.DataFrame,
    n_neighbors: int,
    timewindow_weeks: int,
    feature_columns: List[str],
    batch_size: int = 1000, # batch_size is now handled by the calling function
    logger=None
) -> Tuple[Dict, pd.DataFrame, pd.DataFrame]:
    """
    修正后的PSM-KNN匹配函数，采用动态候选集策略以避免内存溢出
    """
    if logger is None:
        logger = logging.getLogger()

    logger.info("Starting memory-efficient PSM with NearestNeighbors...")

    # 准备处理组和控制组数据
    treatment_df = treatment_repos_with_left_date.copy()
    control_df = productivity_metric_data[
        ~productivity_metric_data['repo_name'].isin(set(treatment_df['repo_name']))
    ].copy()

    # 预计算控制组中发生离职的周，用于后续过滤
    control_treatment_weeks = control_df[control_df['someone_left'] == 1].groupby('repo_name')['standardized_time_weeks'].apply(list)

    logger.info(f"Processing {len(treatment_df)} treatment events.")

    matched_pairs = {}

    # 遍历每一个处理事件
    for i, t_row in treatment_df.iterrows():
        t_repo = t_row["repo_name"]
        t_time = t_row["standardized_time_weeks"]
        t_burst = t_row["burst"]
        t_features = t_row[feature_columns].values.reshape(1, -1)

        # 1. 动态筛选候选控制集 (关键优化)
        # 首先基于时间窗口进行粗筛
        time_window_start = t_time - 4
        time_window_end = t_time + 4

        candidate_controls = control_df[
            (control_df['standardized_time_weeks'] >= time_window_start) &
            (control_df['standardized_time_weeks'] <= time_window_end)
        ].copy()

        # 过滤掉本身在窗口期内有离职事件的控制项目
        valid_candidates = []
        for _, c_row in candidate_controls.iterrows():
            c_repo = c_row['repo_name']
            c_time = c_row['standardized_time_weeks']

            repo_attrition_weeks = control_treatment_weeks.get(c_repo)
            if repo_attrition_weeks:
                # 检查在匹配点的前后 timewindow_weeks 内是否有离职
                in_window = any(c_time - timewindow_weeks <= week <= c_time + timewindow_weeks for week in repo_attrition_weeks)
                if not in_window:
                    valid_candidates.append(c_row)
            else: # 如果该项目从未有过离职，则肯定是有效的
                valid_candidates.append(c_row)

        if not valid_candidates:
            logger.warning(f"No valid control candidates for {t_repo} at time {t_time} after filtering. Skipping.")
            continue

        candidate_df = pd.DataFrame(valid_candidates).reset_index(drop=True)

        if len(candidate_df) < n_neighbors:
            logger.warning(f"Not enough candidates ({len(candidate_df)}) for {t_repo} to find {n_neighbors} neighbors. Skipping.")
            continue

        # 2. 对小范围的候选集构建临时索引
        X_control_temp = candidate_df[feature_columns].values
        nn_model = NearestNeighbors(n_neighbors=n_neighbors, algorithm='auto')
        nn_model.fit(X_control_temp)

        # 3. 在临时索引中查找近邻
        distances, indices = nn_model.kneighbors(t_features)

        # 4. 提取匹配结果
        matched_controls_info = []
        # 使用set来确保每个control repo只被匹配一次
        used_control_repos = set()

        # indices[0] 包含了最近的 n_neighbors 个候选者的索引
        for idx in indices[0]:
            control_row = candidate_df.iloc[idx]
            control_repo = control_row["repo_name"]

            if control_repo not in used_control_repos:
                matched_controls_info.append({
                    "repo_name": control_repo,
                    "matched_time": control_row["standardized_time_weeks"],
                    "features": control_row[feature_columns].values,
                })
                used_control_repos.add(control_repo)

        if matched_controls_info:
            matched_pairs[t_burst] = {
                "burst": t_burst,
                "repo_name": t_repo,
                "treatment_time": t_time,
                "controls": matched_controls_info,
                "treatment_features": t_features.flatten(),
                "control_features": np.array([c["features"] for c in matched_controls_info]),
            }
            if (i + 1) % 100 == 0: # 每处理100个打印一次日志
                 logger.info(f"Processed {i + 1}/{len(treatment_df)} events. Matched {t_repo} with {len(matched_controls_info)} controls.")
        else:
            logger.warning(f"Could not find valid unique controls for {t_repo} at time {t_time}.")

    logger.info(f"Matching completed. Total matched pairs: {len(matched_pairs)}")

    # 返回匹配对，以及原始的处理组和控制组数据用于后续分析
    return matched_pairs, treatment_df, control_df

def test_improved_psm():
    """测试改进版PSM算法"""
    print("="*60)
    print("TESTING IMPROVED PSM ALGORITHM")
    print("="*60)
    
    start_time = time.time()
    
    # 读取测试数据
    test_file = 'result/20250730_did_result/productivity_with_propensity_scores_with_attritions_365_test_sample.csv'
    print(f"Loading test data from: {test_file}")
    p_test = pd.read_csv(test_file)
    print(f"Test data shape: {p_test.shape}")
    
    # 数据预处理 - 完全按照原始逻辑
    p_test_attrition = p_test[p_test['someone_left'] == 1].copy()
    p_test_attrition = p_test_attrition[p_test_attrition['feature_sigmod_add'].notnull()]
    p_test_attrition = p_test_attrition[p_test_attrition['feature_sigmod_add'] != 0.5]  # 使用修改后的条件
    
    print(f"Attrition data shape after filtering: {p_test_attrition.shape}")
    
    p_test = p_test.fillna(0)
    p_test_attrition = p_test_attrition.fillna(0)
    
    # 转换类型
    if 'burst' in p_test_attrition.columns:
        p_test_attrition['burst'] = p_test_attrition['burst'].astype(int)
    
    # 调用 PSM 函数进行匹配
    print("Starting PSM matching...")
    matched_pairs, treatment_features_df, control_features_df = compile_control_group_psm_knn(
        p_test_attrition,
        p_test['repo_name'].tolist(),
        p_test,
        n_neighbors=5,
        timewindow_weeks=12,
        feature_columns=['feature_sigmod_add'],
        batch_size=1000
    )
    
    end_time = time.time()
    processing_time = end_time - start_time
    
    print(f"\n=== IMPROVED PSM RESULTS ===")
    print(f"Processing time: {processing_time:.2f} seconds")
    print(f"Total matched pairs: {len(matched_pairs)}")
    print(f"Treatment features shape: {treatment_features_df.shape}")
    print(f"Control features shape: {control_features_df.shape}")
    
    # 保存结果用于对比
    output_file = 'test_results_improved.pkl'
    with open(output_file, 'wb') as f:
        pickle.dump({
            'matched_pairs': matched_pairs,
            'treatment_features_df': treatment_features_df,
            'control_features_df': control_features_df,
            'processing_time': processing_time
        }, f)
    print(f"Results saved to: {output_file}")
    
    # 显示一些详细信息
    if matched_pairs:
        first_key = list(matched_pairs.keys())[0]
        first_match = matched_pairs[first_key]
        print(f"\nSample match (burst {first_key}):")
        print(f"  Repo: {first_match['repo_name']}")
        print(f"  Treatment time: {first_match['treatment_time']}")
        print(f"  Number of controls: {len(first_match['controls'])}")
        print(f"  Control repos: {[c['repo_name'] for c in first_match['controls']]}")
    
    return matched_pairs, processing_time

if __name__ == "__main__":
    print(f"Test started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    test_improved_psm()
    print(f"Test completed at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
